'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

// Navigation Bar Component
function NavBar() {
  return (
    <nav className="bg-blue-600 text-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Link href="/" className="text-xl font-bold hover:text-blue-200">
                Funding Analysis
              </Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link href="/" className="hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">
                  Dashboard
                </Link>
                <Link href="/uk" className="bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">
                  UK Opportunities
                </Link>
                <Link href="/eu" className="hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">
                  EU Opportunities
                </Link>
                <a href="#" className="hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">
                  Settings
                </a>
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <button className="bg-blue-500 hover:bg-blue-700 px-4 py-2 rounded-md text-sm font-medium">
              Refresh Data
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}

// Loading Component
function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>
  );
}

// Error Component
function ErrorMessage({ message, onRetry }) {
  return (
    <div className="bg-red-50 border border-red-200 rounded-md p-4 m-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">Error loading data</h3>
          <div className="mt-2 text-sm text-red-700">
            <p>{message}</p>
          </div>
          <div className="mt-4">
            <button
              onClick={onRetry}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// UK Opportunity Table Component
function UKOpportunityTable({ opportunities }) {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  if (!opportunities || opportunities.length === 0) {
    return (
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">UK Funding Opportunities</h2>
          <p className="text-gray-500">No UK opportunities available</p>
        </div>
      </div>
    );
  }

  // Filter opportunities based on relevance and search term
  const filteredOpportunities = opportunities.filter(opp => {
    const matchesFilter = filter === 'all' ||
      (filter === 'relevant' && (opp.Pertinence === 'Yes' || opp['Pertinence LLM'] === 'Oui')) ||
      (filter === 'not-relevant' && (opp.Pertinence === 'No' || opp['Pertinence LLM'] === 'Non'));

    const matchesSearch = searchTerm === '' ||
      opp.Title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      opp.Description?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  const getRelevanceStatus = (opp) => {
    if (opp.Pertinence === 'Yes' || opp['Pertinence LLM'] === 'Oui') {
      return { text: 'Relevant', color: 'bg-green-100 text-green-800' };
    } else if (opp.Pertinence === 'No' || opp['Pertinence LLM'] === 'Non') {
      return { text: 'Not Relevant', color: 'bg-red-100 text-red-800' };
    }
    return { text: 'Unknown', color: 'bg-gray-100 text-gray-800' };
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900">UK Funding Opportunities</h2>
          <span className="text-sm text-gray-500">
            {filteredOpportunities.length} of {opportunities.length} opportunities
          </span>
        </div>
        
        {/* Filters */}
        <div className="mb-4 flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search opportunities..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Opportunities</option>
              <option value="relevant">Relevant Only</option>
              <option value="not-relevant">Not Relevant</option>
            </select>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Title
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Opening Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Deadline
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Matching Keywords
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredOpportunities.map((opportunity, index) => {
                const relevanceStatus = getRelevanceStatus(opportunity);
                return (
                  <tr key={opportunity.URL || index} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900 max-w-xs">
                        {opportunity.Title}
                      </div>
                      <div className="text-sm text-gray-500 max-w-xs truncate">
                        {opportunity.Description}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${relevanceStatus.color}`}>
                        {relevanceStatus.text}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {opportunity.Start_date || 'Not specified'}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {opportunity.Deadline || 'Not specified'}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 max-w-xs">
                        {opportunity['Matching Word(s)'] ? (
                          <div className="flex flex-wrap gap-1">
                            {opportunity['Matching Word(s)'].split(', ').slice(0, 3).map((keyword, index) => (
                              <span key={index} className="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                {keyword}
                              </span>
                            ))}
                            {opportunity['Matching Word(s)'].split(', ').length > 3 && (
                              <span className="text-xs text-gray-500">
                                +{opportunity['Matching Word(s)'].split(', ').length - 3} more
                              </span>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400">No keywords</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm font-medium">
                      <a
                        href={opportunity.URL}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-900"
                      >
                        View Details
                      </a>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {filteredOpportunities.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">No opportunities match your current filters</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default function UKPage() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // For now, using the provided mock data
      const mockData = {
        "success": true,
        "message": "Combined analysis results retrieved successfully",
        "timestamp": "2025-07-21T11:13:29.849Z",
        "data": {
          "analysisType": "Combined EU & UK Funding Opportunities",
          "lastUpdate": null,
          "overallStatus": "not_started",
          "eu": {
            "status": "not_started",
            "fileAvailable": true,
            "statistics": {
              "projectsCount": 88,
              "relevantCount": "88",
              "llmAnalyzedCount": "0"
            },
            "data": [
              {
                "URL": "https://ec.europa.eu/info/funding-tenders/opportunities/portal/screen/opportunities/topic-details/HORIZON-CL5-2026-02-D4-03?isExactMatch=true&status=31094501,31094502&order=DESC&pageNumber=1&pageSize=50&sortBy=startDate",
                "Pertinence": "Yes",
                "Matching Word(s)": "climate resilience, social innovation",
                "Title": "Innovative pathways for low carbon and climate resilient building stock and built environment (Built4People Partnership)",
                "Status": "Forthcoming",
                "Start_date": "16 September 2025",
                "Deadline": "17 February 2026",
                "Pertinence LLM": "Non",
                "Résumé LLM": "L'appel à projets est vide, il n'y a pas de description du projet, ce qui rend impossible la comparaison avec les projets précédents de l'entreprise. Il est donc difficile de déterminer si l'opportunité est pertinente ou non.",
                "Réponse brute": "Pertinence : Non\r\n\r\nProjets similaires détectés : Aucun\r\n\r\nRésumé rapide : L'appel à projets est vide, il n'y a pas de description du projet, ce qui rend impossible la comparaison avec les projets précédents de l'entreprise. Il est donc difficile de déterminer si l'opportunité est pertinente ou non."
              }
            ]
          },
          "uk": {
            "status": "not_started",
            "fileAvailable": true,
            "statistics": {
              "projectsCount": 90,
              "relevantCount": "90",
              "llmAnalyzedCount": "0"
            },
            "data": [
              {
                "Title": "Smart Data Research UK Fellowships",
                "URL": "https://www.ukri.org/opportunity/smart-data-research-uk-fellowships/",
                "Description": "Apply for a fellowship to undertake innovative and impact-focused research using smart data. Projects should address a significant real-world challenge which is relevant to the UK.",
                "Start_date": "15 July 2025 9:00am UK time",
                "Deadline": "23 September 2025 4:00pm UK time",
                "Pertinence": "Yes",
                "Matching Word(s)": "capacity building, artificial intelligence, economic impact",
                "Pertinence LLM": "Non",
                "Résumé LLM": "L'appel à projets concerne le soutien à l'entreprendariat social et à l'innovation sociale, ce qui est en lien avec les expériences de l'entreprise dans la création d'écosystèmes d'entreprendre (National Entrepreneurship Program) et le soutien à la maturation technologique de start-ups (FACTORIAT). L'entreprise a démontré sa capacité à mettre en place des programmes de soutien à l'entreprendre et à l'innovation, ce qui la rend apte à répondre à cet appel à projets.",
                "Réponse brute": "Analyse de l'opportunité de projet :\r\n\r\n**Appel à projets** : \"Support to social entrepreneurship and social innovation in favour of vulnerable groups\" (Soutien à l'entreprendariat social et à l'innovation sociale en faveur des groupes vulnérables)\r\n\r\n**Pertinence** : Oui\r\n\r\n**Projets similaires détectés** : National Entrepreneurship Program, FACTORIAT\r\n\r\n**Résumé rapide** : L'appel à projets concerne le soutien à l'entreprendariat social et à l'innovation sociale, ce qui est en lien avec les expériences de l'entreprise dans la création d'écosystèmes d'entreprendre (National Entrepreneurship Program) et le soutien à la maturation technologique de start-ups (FACTORIAT). L'entreprise a démontré sa capacité à mettre en place des programmes de soutien à l'entreprendre et à l'innovation, ce qui la rend apte à répondre à cet appel à projets."
              },
              {
                "Title": "Institutional diversity fund (invite only)",
                "URL": "https://www.ukri.org/opportunity/institutional-diversity-fund-invite-only/",
                "Description": "The purpose of this opportunity is to provide strategic research infrastructure base funding, supporting independent research organisations to support the diverse social science ecosystem in the UK.",
                "Start_date": "21 July 2025 9:00am UK time",
                "Deadline": "16 September 2025 4:00pm UK time",
                "Pertinence": "Yes",
                "Matching Word(s)": "artificial intelligence",
                "Pertinence LLM": "Non",
                "Résumé LLM": "Les projets précédemment réalisés par l'entreprise sont principalement liés à l'incubation, l'accélération et le financement de startups, ainsi qu'à la création d'écosystèmes d'entrepreneuriat, alors que l'appel à projet ESRC concerne le soutien à la recherche en sciences sociales et à l'infrastructure de recherche au sein d'organisations de recherche indépendantes britanniques. Les objectifs et les domaines d'intervention sont donc très différents.",
                "Réponse brute": "Pertinence : Non\r\n\r\nProjets similaires détectés : Aucun\r\n\r\nRésumé rapide : Les projets précédemment réalisés par l'entreprise sont principalement liés à l'incubation, l'accélération et le financement de startups, ainsi qu'à la création d'écosystèmes d'entrepreneuriat, alors que l'appel à projet ESRC concerne le soutien à la recherche en sciences sociales et à l'infrastructure de recherche au sein d'organisations de recherche indépendantes britanniques. Les objectifs et les domaines d'intervention sont donc très différents."
              },
              {
                "Title": "Gap fund for early-stage development of new healthcare interventions",
                "URL": "https://www.ukri.org/opportunity/funding-for-early-stage-development-of-new-healthcare-interventions/",
                "Description": "Apply to the Developmental Pathway Gap Fund to address a single-step evidence gap and de-risk the development of a new medicine, medical device, diagnostic test or other medical intervention.",
                "Start_date": "10 July 2025 9:00am UK time",
                "Deadline": "12 November 2025 4:00pm UK time",
                "Pertinence": "Yes",
                "Matching Word(s)": "due diligence, development strategy, technology transfer, intellectual property, service delivery, artificial intelligence, applied research",
                "Pertinence LLM": "Non",
                "Résumé LLM": "L'appel à projets concerne des projets de recherche médicale et de développement de médicaments, ce qui n'est pas en ligne avec les compétences et les axes de l'entreprise qui sont plutôt tournés vers l'incubation, l'accélération, le développement de startups et la stratégie pour l'économie numérique. Les projets de l'entreprise (FACTORIAT, National Entrepreneurship Program, Arab Bank Strategy) n'ont pas de lien direct avec la santé, la médecine ou le développement de médicaments.",
                "Réponse brute": "Pertinence : Non\r\n\r\nProjets similaires détectés : Aucun\r\n\r\nRésumé rapide : L'appel à projets concerne des projets de recherche médicale et de développement de médicaments, ce qui n'est pas en ligne avec les compétences et les axes de l'entreprise qui sont plutôt tournés vers l'incubation, l'accélération, le développement de startups et la stratégie pour l'économie numérique. Les projets de l'entreprise (FACTORIAT, National Entrepreneurship Program, Arab Bank Strategy) n'ont pas de lien direct avec la santé, la médecine ou le développement de médicaments."
              },
              {
                "Title": "Digitise UK natural science collections",
                "URL": "https://www.ukri.org/opportunity/digitise-uk-natural-science-collections/",
                "Description": "Apply for funding to establish a Digitisation Hub for Natural Science Collections as part of the Distributed System of Scientific Collections (DiSSCo) UK programme.",
                "Start_date": "8 July 2025 9:00am UK time",
                "Deadline": "16 September 2025 4:00pm UK time",
                "Pertinence": "Yes",
                "Matching Word(s)": "capacity building, project management, due diligence, artificial intelligence",
                "Pertinence LLM": "Non",
                "Résumé LLM": "L'appel à projets concerne la création de hubs de numérisation pour les collections scientifiques naturelles au Royaume-Uni, ce qui est très spécifique et différent des projets déjà réalisés par l'entreprise, qui se concentrent sur l'incubation, la formation, la stratégie et le financement pour les startups et les écosystèmes d'entreprises, principalement dans les domaines de la Deeptech et de l'économie numérique. Il n'y a pas desimilarité claire entre les projets précédemment réalisés et cet appel à projets.",
                "Réponse brute": "Pertinence : Non\r\n\r\nProjets similaires détectés : Aucun\r\n\r\nRésumé rapide : L'appel à projets concerne la création de hubs de numérisation pour les collections scientifiques naturelles au Royaume-Uni, ce qui est très spécifique et différent des projets déjà réalisés par l'entreprise, qui se concentrent sur l'incubation, la formation, la stratégie et le financement pour les startups et les écosystèmes d'entreprises, principalement dans les domaines de la Deeptech et de l'économie numérique. Il n'y a pas desimilarité claire entre les projets précédemment réalisés et cet appel à projets."
              }
            ]
          }
        }
      };

      setData(mockData);
    } catch (err) {
      setError(err.message);
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Extract UK opportunities from the new data structure
  const ukOpportunities = data?.data?.uk?.data || [];

  return (
    <div className="min-h-screen bg-gray-100">
      <NavBar />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">UK Funding Opportunities</h1>
          <p className="text-gray-600">
            Total UK Opportunities: {ukOpportunities.length}
          </p>
        </div>

        {loading && <LoadingSpinner />}
        
        {error && (
          <ErrorMessage 
            message={error} 
            onRetry={fetchData}
          />
        )}
        
        {!loading && !error && (
          <UKOpportunityTable opportunities={ukOpportunities} />
        )}
      </main>
    </div>
  );
}
