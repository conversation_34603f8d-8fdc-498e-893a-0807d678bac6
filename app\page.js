'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

// Navigation Bar Component
function NavBar() {
  return (
    <nav className="bg-blue-600 text-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Link href="/" className="text-xl font-bold hover:text-blue-200">
                Funding Analysis
              </Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link href="/" className="bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">
                  Dashboard
                </Link>
                <Link href="/uk" className="hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">
                  UK Opportunities
                </Link>
                <Link href="/eu" className="hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">
                  EU Opportunities
                </Link>
                <a href="#" className="hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">
                  Settings
                </a>
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <button className="bg-blue-500 hover:bg-blue-700 px-4 py-2 rounded-md text-sm font-medium">
              Refresh Data
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}

// Loading Component
function LoadingSpinner() {
  return (
    <div className="flex justify-center items-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>
  );
}

// Error Component
function ErrorMessage({ message, onRetry }) {
  return (
    <div className="bg-red-50 border border-red-200 rounded-md p-4 m-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">Error loading data</h3>
          <div className="mt-2 text-sm text-red-700">
            <p>{message}</p>
          </div>
          <div className="mt-4">
            <button
              onClick={onRetry}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Navigation Cards Component
function NavigationCards({ data }) {
  // Calculate statistics
  let ukCount = 0;
  let euCount = 0;
  let ukRelevant = 0;
  let euRelevant = 0;

  if (data && Array.isArray(data)) {
    data.forEach(analysis => {
      if (analysis.opportunities && Array.isArray(analysis.opportunities)) {
        analysis.opportunities.forEach(opportunity => {
          const isRelevant = opportunity.pertinence === 'Yes' || opportunity.pertinenceLlm === 'Oui';

          if (analysis.analysisType === 'UK_FUNDING_OPPORTUNITIES' ||
              opportunity.lien?.includes('ukri.org') ||
              opportunity.url?.includes('ukri.org')) {
            ukCount++;
            if (isRelevant) ukRelevant++;
          } else if (analysis.analysisType === 'EU_FUNDING_OPPORTUNITIES' ||
                     opportunity.lien?.includes('europa.eu') ||
                     opportunity.url?.includes('europa.eu')) {
            euCount++;
            if (isRelevant) euRelevant++;
          } else {
            // Default to UK if unclear
            ukCount++;
            if (isRelevant) ukRelevant++;
          }
        });
      }
    });
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Funding Opportunities Dashboard</h1>
        <p className="text-lg text-gray-600">
          Choose a region to explore funding opportunities
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        {/* UK Card */}
        <Link href="/uk">
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 cursor-pointer border-2 border-transparent hover:border-blue-500">
            <div className="p-8">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h2 className="text-2xl font-bold text-gray-900">UK Opportunities</h2>
                    <p className="text-gray-600">United Kingdom funding programs</p>
                  </div>
                </div>
                <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>

              <div className="grid grid-cols-2 gap-4 mt-6">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{ukCount}</div>
                  <div className="text-sm text-gray-600">Total Opportunities</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{ukRelevant}</div>
                  <div className="text-sm text-gray-600">Relevant</div>
                </div>
              </div>

              <div className="mt-6 flex items-center text-blue-600">
                <span className="text-sm font-medium">View UK opportunities</span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </div>
            </div>
          </div>
        </Link>

        {/* EU Card */}
        <Link href="/eu">
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 cursor-pointer border-2 border-transparent hover:border-blue-500">
            <div className="p-8">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h2 className="text-2xl font-bold text-gray-900">EU Opportunities</h2>
                    <p className="text-gray-600">European Union funding programs</p>
                  </div>
                </div>
                <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>

              <div className="grid grid-cols-2 gap-4 mt-6">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{euCount}</div>
                  <div className="text-sm text-gray-600">Total Opportunities</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{euRelevant}</div>
                  <div className="text-sm text-gray-600">Relevant</div>
                </div>
              </div>

              <div className="mt-6 flex items-center text-blue-600">
                <span className="text-sm font-medium">View EU opportunities</span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* Summary Stats */}
      <div className="mt-12 bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Overview</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{ukCount + euCount}</div>
            <div className="text-sm text-gray-600">Total Opportunities</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{ukRelevant + euRelevant}</div>
            <div className="text-sm text-gray-600">Relevant</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{ukCount}</div>
            <div className="text-sm text-gray-600">UK Programs</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{euCount}</div>
            <div className="text-sm text-gray-600">EU Programs</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Home() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Using the provided mock data structure
      const mockData = {
        "success": true,
        "message": "Combined analysis results retrieved successfully",
        "timestamp": "2025-07-21T11:13:29.849Z",
        "data": {
          "analysisType": "Combined EU & UK Funding Opportunities",
          "lastUpdate": null,
          "overallStatus": "not_started",
          "eu": {
            "status": "not_started",
            "fileAvailable": true,
            "statistics": {
              "projectsCount": 88,
              "relevantCount": "88",
              "llmAnalyzedCount": "0"
            },
            "data": [
              {
                "URL": "https://ec.europa.eu/info/funding-tenders/opportunities/portal/screen/opportunities/topic-details/HORIZON-CL5-2026-02-D4-03?isExactMatch=true&status=31094501,31094502&order=DESC&pageNumber=1&pageSize=50&sortBy=startDate",
                "Pertinence": "Yes",
                "Matching Word(s)": "climate resilience, social innovation",
                "Title": "Innovative pathways for low carbon and climate resilient building stock and built environment (Built4People Partnership)",
                "Status": "Forthcoming",
                "Start_date": "16 September 2025",
                "Deadline": "17 February 2026",
                "Pertinence LLM": "Non",
                "Résumé LLM": "L'appel à projets est vide, il n'y a pas de description du projet, ce qui rend impossible la comparaison avec les projets précédents de l'entreprise. Il est donc difficile de déterminer si l'opportunité est pertinente ou non.",
                "Réponse brute": "Pertinence : Non\r\n\r\nProjets similaires détectés : Aucun\r\n\r\nRésumé rapide : L'appel à projets est vide, il n'y a pas de description du projet, ce qui rend impossible la comparaison avec les projets précédents de l'entreprise. Il est donc difficile de déterminer si l'opportunité est pertinente ou non."
              },
              {
                "URL": "https://ec.europa.eu/info/funding-tenders/opportunities/portal/screen/opportunities/topic-details/HORIZON-CL5-2026-02-D3-08?isExactMatch=true&status=31094501,31094502&order=DESC&pageNumber=1&pageSize=50&sortBy=startDate",
                "Pertinence": "Yes",
                "Matching Word(s)": "project design, renewable energy, economic impact",
                "Title": "Understand and minimise the environmental impacts of offshore wind energy",
                "Status": "Forthcoming",
                "Start_date": "16 September 2025",
                "Deadline": "17 February 2026",
                "Pertinence LLM": "Non",
                "Résumé LLM": "**",
                "Réponse brute": "Bonjour !\r\n\r\nAprès avoir analysé l'appel à projets et les projets déjà réalisés par l'entreprise, voici ma réponse :\r\n\r\n**Pertinence : Non**\r\n\r\n**Projets similaires détectés : Aucun**\r\n\r\n**Résumé rapide :**\r\nL'appel à projets concerne spécifiquement la mise en œuvre de technologies pour réduire les impacts environnementaux de l'énergie éolienne offshore, ce qui est très loin des compétences et des domaines d'intervention de l'entreprise qui sont principalement liés à l'incubation, l'accélération et le développement de startups Deeptech, ainsi que la création d'écosystèmes d'entreprises. Il n'y a donc pas de similarité évidente entre cet appel à projets et les projets déjà réalisés par l'entreprise."
              },
              {
                "URL": "https://ec.europa.eu/info/funding-tenders/opportunities/portal/screen/opportunities/topic-details/HORIZON-CL5-2026-02-D4-02?isExactMatch=true&status=31094501,31094502&order=DESC&pageNumber=1&pageSize=50&sortBy=startDate",
                "Pertinence": "Yes",
                "Matching Word(s)": "renewable energy",
                "Title": "Smarter buildings as part of the energy system for increased efficiency and flexibility – Societal Readiness Pilot",
                "Status": "Forthcoming",
                "Start_date": "16 September 2025",
                "Deadline": "17 February 2026",
                "Pertinence LLM": "Non",
                "Résumé LLM": "L'appel à projet concerne spécifiquement le domaine de la coévidente avec les projets déjà réalisés par l'entreprise.",
                "Réponse brute": "Pertinence : Non\r\n\r\nProjets similaires détectés : Aucun\r\n\r\nRésumé rapide : L'appel à projet concerne spécifiquement le domaine de la construction et de l'énergie (bâtiments i numérique, la formation, etc. Il n'y a aucunesimilarité évidente avec les projets déjà réalisés par l'entreprise."
              }
            ]
          },
          "uk": {
            "status": "not_started",
            "fileAvailable": true,
            "statistics": {
              "projectsCount": 90,
              "relevantCount": "90",
              "llmAnalyzedCount": "0"
            },
            "data": [
              {
                "Title": "Smart Data Research UK Fellowships",
                "URL": "https://www.ukri.org/opportunity/smart-data-research-uk-fellowships/",
                "Description": "Apply for a fellowship to undertake innovative and impact-focused research using smart data. Projects should address a significant real-world challenge which is relevant to the UK.",
                "Start_date": "15 July 2025 9:00am UK time",
                "Deadline": "23 September 2025 4:00pm UK time",
                "Pertinence": "Yes",
                "Matching Word(s)": "capacity building, artificial intelligence, economic impact",
                "Pertinence LLM": "Non",
                "Résumé LLM": "L'appel à projets concerne le soutien à l'entreprendariat social et à l'innovation sociale, ce qui est en lien avec les expériences de l'entreprise dans la création d'écosystèmes d'entreprendre (National Entrepreneurship Program) et le soutien à la maturation technologique de start-ups (FACTORIAT). L'entreprise a démontré sa capacité à mettre en place des programmes de soutien à l'entreprendre et à l'innovation, ce qui la rend apte à répondre à cet appel à projets.",
                "Réponse brute": "Analyse de l'opportunité de projet :\r\n\r\n**Appel à projets** : \"Support to social entrepreneurship and social innovation in favour of vulnerable groups\" (Soutien à l'entreprendariat social et à l'innovation sociale en faveur des groupes vulnérables)\r\n\r\n**Pertinence** : Oui\r\n\r\n**Projets similaires détectés** : National Entrepreneurship Program, FACTORIAT\r\n\r\n**Résumé rapide** : L'appel à projets concerne le soutien à l'entreprendariat social et à l'innovation sociale, ce qui est en lien avec les expériences de l'entreprise dans la création d'écosystèmes d'entreprendre (National Entrepreneurship Program) et le soutien à la maturation technologique de start-ups (FACTORIAT). L'entreprise a démontré sa capacité à mettre en place des programmes de soutien à l'entreprendre et à l'innovation, ce qui la rend apte à répondre à cet appel à projets."
              },
              {
                "Title": "Institutional diversity fund (invite only)",
                "URL": "https://www.ukri.org/opportunity/institutional-diversity-fund-invite-only/",
                "Description": "The purpose of this opportunity is to provide strategic research infrastructure base funding, supporting independent research organisations to support the diverse social science ecosystem in the UK.",
                "Start_date": "21 July 2025 9:00am UK time",
                "Deadline": "16 September 2025 4:00pm UK time",
                "Pertinence": "Yes",
                "Matching Word(s)": "artificial intelligence",
                "Pertinence LLM": "Non",
                "Résumé LLM": "Les projets précédemment réalisés par l'entreprise sont principalement liés à l'incubation, l'accélération et le financement de startups, ainsi qu'à la création d'écosystèmes d'entrepreneuriat, alors que l'appel à projet ESRC concerne le soutien à la recherche en sciences sociales et à l'infrastructure de recherche au sein d'organisations de recherche indépendantes britanniques. Les objectifs et les domaines d'intervention sont donc très différents.",
                "Réponse brute": "Pertinence : Non\r\n\r\nProjets similaires détectés : Aucun\r\n\r\nRésumé rapide : Les projets précédemment réalisés par l'entreprise sont principalement liés à l'incubation, l'accélération et le financement de startups, ainsi qu'à la création d'écosystèmes d'entrepreneuriat, alors que l'appel à projet ESRC concerne le soutien à la recherche en sciences sociales et à l'infrastructure de recherche au sein d'organisations de recherche indépendantes britanniques. Les objectifs et les domaines d'intervention sont donc très différents."
              },
              {
                "Title": "Gap fund for early-stage development of new healthcare interventions",
                "URL": "https://www.ukri.org/opportunity/funding-for-early-stage-development-of-new-healthcare-interventions/",
                "Description": "Apply to the Developmental Pathway Gap Fund to address a single-step evidence gap and de-risk the development of a new medicine, medical device, diagnostic test or other medical intervention.",
                "Start_date": "10 July 2025 9:00am UK time",
                "Deadline": "12 November 2025 4:00pm UK time",
                "Pertinence": "Yes",
                "Matching Word(s)": "due diligence, development strategy, technology transfer, intellectual property, service delivery, artificial intelligence, applied research",
                "Pertinence LLM": "Non",
                "Résumé LLM": "L'appel à projets concerne des projets de recherche médicale et de développement de médicaments, ce qui n'est pas en ligne avec les compétences et les axes de l'entreprise qui sont plutôt tournés vers l'incubation, l'accélération, le développement de startups et la stratégie pour l'économie numérique. Les projets de l'entreprise (FACTORIAT, National Entrepreneurship Program, Arab Bank Strategy) n'ont pas de lien direct avec la santé, la médecine ou le développement de médicaments.",
                "Réponse brute": "Pertinence : Non\r\n\r\nProjets similaires détectés : Aucun\r\n\r\nRésumé rapide : L'appel à projets concerne des projets de recherche médicale et de développement de médicaments, ce qui n'est pas en ligne avec les compétences et les axes de l'entreprise qui sont plutôt tournés vers l'incubation, l'accélération, le développement de startups et la stratégie pour l'économie numérique. Les projets de l'entreprise (FACTORIAT, National Entrepreneurship Program, Arab Bank Strategy) n'ont pas de lien direct avec la santé, la médecine ou le développement de médicaments."
              },
              {
                "Title": "Digitise UK natural science collections",
                "URL": "https://www.ukri.org/opportunity/digitise-uk-natural-science-collections/",
                "Description": "Apply for funding to establish a Digitisation Hub for Natural Science Collections as part of the Distributed System of Scientific Collections (DiSSCo) UK programme.",
                "Start_date": "8 July 2025 9:00am UK time",
                "Deadline": "16 September 2025 4:00pm UK time",
                "Pertinence": "Yes",
                "Matching Word(s)": "capacity building, project management, due diligence, artificial intelligence",
                "Pertinence LLM": "Non",
                "Résumé LLM": "L'appel à projets concerne la création de hubs de numérisation pour les collections scientifiques naturelles au Royaume-Uni, ce qui est très spécifique et différent des projets déjà réalisés par l'entreprise, qui se concentrent sur l'incubation, la formation, la stratégie et le financement pour les startups et les écosystèmes d'entreprises, principalement dans les domaines de la Deeptech et de l'économie numérique. Il n'y a pas desimilarité claire entre les projets précédemment réalisés et cet appel à projets.",
                "Réponse brute": "Pertinence : Non\r\n\r\nProjets similaires détectés : Aucun\r\n\r\nRésumé rapide : L'appel à projets concerne la création de hubs de numérisation pour les collections scientifiques naturelles au Royaume-Uni, ce qui est très spécifique et différent des projets déjà réalisés par l'entreprise, qui se concentrent sur l'incubation, la formation, la stratégie et le financement pour les startups et les écosystèmes d'entreprises, principalement dans les domaines de la Deeptech et de l'économie numérique. Il n'y a pas desimilarité claire entre les projets précédemment réalisés et cet appel à projets."
              }
            ]
          }
        }
      };

      console.log('Data fetched:', mockData);
      setData(mockData);
    } catch (err) {
      setError(err.message);
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-100">
      <NavBar />

      <main>
        {loading && <LoadingSpinner />}

        {error && (
          <ErrorMessage
            message={error}
            onRetry={fetchData}
          />
        )}

        {!loading && !error && data && (
          <NavigationCards data={data} />
        )}

        {!loading && !error && !data && (
          <div className="text-center py-8">
            <p className="text-gray-500">No data available</p>
          </div>
        )}
      </main>
    </div>
  );
}
